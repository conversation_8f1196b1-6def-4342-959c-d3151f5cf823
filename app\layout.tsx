import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'GhostLayer - AI Text Humanization Tool',
  description: 'Transform AI-generated content into natural, human-like writing while preserving meaning and intent.',
  keywords: 'AI text humanization, content transformation, AI detection bypass, text paraphrasing',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <script
          defer
          data-domain="ghostlayer.app"
          src="https://plausible.io/js/script.js"
        ></script>
      </head>
      <body className={inter.className}>{children}</body>
    </html>
  );
}