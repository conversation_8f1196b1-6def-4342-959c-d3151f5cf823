import { ProcessingOptions } from '@/types';

interface SynonymMap {
  [key: string]: string[];
}

// Comprehensive synonym dictionary organized by categories
const synonyms: SynonymMap = {
  // Common verbs
  'make': ['create', 'produce', 'generate', 'construct', 'develop', 'build', 'form', 'establish'],
  'get': ['obtain', 'acquire', 'receive', 'secure', 'gain', 'attain', 'procure', 'fetch'],
  'use': ['utilize', 'employ', 'apply', 'implement', 'leverage', 'adopt', 'harness', 'exploit'],
  'show': ['demonstrate', 'reveal', 'display', 'illustrate', 'exhibit', 'present', 'manifest', 'indicate'],
  'give': ['provide', 'offer', 'supply', 'deliver', 'present', 'grant', 'bestow', 'furnish'],
  'take': ['require', 'consume', 'demand', 'involve', 'necessitate', 'call for', 'seize', 'grasp'],
  'find': ['discover', 'locate', 'identify', 'uncover', 'detect', 'come across', 'encounter', 'spot'],
  'think': ['believe', 'consider', 'feel', 'suppose', 'assume', 'reckon', 'contemplate', 'ponder'],
  'know': ['understand', 'realize', 'recognize', 'comprehend', 'grasp', 'appreciate', 'perceive', 'discern'],
  'say': ['state', 'mention', 'declare', 'express', 'articulate', 'convey', 'assert', 'proclaim'],
  'work': ['function', 'operate', 'perform', 'succeed', 'be effective', 'do the job', 'accomplish', 'execute'],
  'help': ['assist', 'aid', 'support', 'facilitate', 'contribute to', 'enable', 'benefit', 'serve'],
  'try': ['attempt', 'endeavor', 'strive', 'seek', 'aim', 'work toward', 'undertake', 'venture'],
  'keep': ['maintain', 'preserve', 'retain', 'sustain', 'continue', 'uphold', 'conserve', 'hold'],
  'start': ['begin', 'commence', 'initiate', 'launch', 'embark on', 'set out', 'kick off', 'trigger'],
  'stop': ['cease', 'halt', 'discontinue', 'end', 'terminate', 'conclude', 'finish', 'suspend'],
  'turn': ['transform', 'convert', 'change', 'shift', 'evolve', 'develop', 'modify', 'alter'],
  'move': ['shift', 'relocate', 'transfer', 'progress', 'advance', 'proceed', 'migrate', 'transport'],
  'run': ['operate', 'manage', 'execute', 'conduct', 'administer', 'oversee', 'control', 'direct'],
  'hold': ['maintain', 'retain', 'keep', 'possess', 'contain', 'support', 'grasp', 'clutch'],
  'bring': ['deliver', 'transport', 'carry', 'convey', 'introduce', 'present', 'fetch', 'escort'],
  'put': ['place', 'position', 'locate', 'situate', 'install', 'establish', 'set', 'arrange'],
  'end': ['conclude', 'finish', 'complete', 'terminate', 'finalize', 'wrap up', 'close', 'cease'],

  // Common adjectives
  'good': ['excellent', 'great', 'fine', 'outstanding', 'superb', 'wonderful', 'remarkable', 'impressive'],
  'bad': ['poor', 'terrible', 'awful', 'disappointing', 'inadequate', 'subpar', 'inferior', 'unsatisfactory'],
  'big': ['large', 'huge', 'massive', 'substantial', 'enormous', 'significant', 'considerable', 'extensive'],
  'small': ['tiny', 'little', 'compact', 'minimal', 'modest', 'minor', 'diminutive', 'petite'],
  'important': ['crucial', 'vital', 'essential', 'significant', 'critical', 'key', 'fundamental', 'paramount'],
  'new': ['recent', 'fresh', 'novel', 'modern', 'contemporary', 'latest', 'current', 'up-to-date'],
  'old': ['previous', 'former', 'earlier', 'past', 'traditional', 'established', 'ancient', 'vintage'],
  'high': ['elevated', 'tall', 'lofty', 'towering', 'superior', 'advanced', 'prominent', 'peak'],
  'low': ['reduced', 'minimal', 'decreased', 'modest', 'limited', 'subdued', 'inferior', 'bottom'],
  'long': ['extended', 'lengthy', 'prolonged', 'extensive', 'drawn-out', 'protracted', 'enduring', 'sustained'],
  'short': ['brief', 'concise', 'quick', 'limited', 'compact', 'abbreviated', 'succinct', 'condensed'],
  'right': ['correct', 'accurate', 'proper', 'appropriate', 'suitable', 'fitting', 'precise', 'exact'],
  'wrong': ['incorrect', 'mistaken', 'erroneous', 'inaccurate', 'improper', 'unsuitable', 'false', 'flawed'],
  'different': ['distinct', 'unique', 'varied', 'diverse', 'alternative', 'separate', 'dissimilar', 'contrasting'],
  'same': ['identical', 'similar', 'equivalent', 'matching', 'comparable', 'alike', 'uniform', 'consistent'],
  'free': ['available', 'unrestricted', 'open', 'accessible', 'complimentary', 'no-cost', 'liberated', 'unbound'],
  'sure': ['certain', 'confident', 'positive', 'convinced', 'definite', 'assured', 'guaranteed', 'undoubtable'],
  'better': ['superior', 'improved', 'enhanced', 'preferable', 'more effective', 'upgraded', 'refined', 'optimized'],
  'best': ['optimal', 'finest', 'top', 'premier', 'excellent', 'outstanding', 'supreme', 'ultimate'],

  // Common adverbs
  'very': ['extremely', 'quite', 'really', 'remarkably', 'exceptionally', 'particularly', 'notably', 'significantly'],
  'also': ['additionally', 'furthermore', 'moreover', 'likewise', 'similarly', 'besides', 'too', 'as well'],
  'only': ['exclusively', 'solely', 'merely', 'just', 'simply', 'nothing but', 'purely', 'entirely'],
  'really': ['truly', 'genuinely', 'actually', 'indeed', 'certainly', 'definitely', 'absolutely', 'positively'],
  'always': ['constantly', 'continuously', 'perpetually', 'invariably', 'consistently', 'forever', 'eternally', 'endlessly'],
  'never': ['not ever', 'at no time', 'under no circumstances', 'not once', 'not at all', 'by no means'],

  // Conjunctions and connectors
  'but': ['however', 'nevertheless', 'yet', 'although', 'nonetheless', 'still', 'though', 'whereas'],
  'because': ['since', 'due to', 'as a result of', 'owing to', 'given that', 'considering', 'for the reason that'],
  'so': ['therefore', 'thus', 'hence', 'consequently', 'as a result', 'accordingly', 'for this reason'],
  'and': ['plus', 'along with', 'together with', 'as well as', 'in addition to', 'furthermore', 'moreover'],

  // Question words
  'why': ['for what reason', 'what purpose', 'the motivation behind', 'the cause of', 'what drives'],
  'how': ['in what way', 'by what method', 'through what process', 'via what means', 'the manner in which'],
  'when': ['at what time', 'during which period', 'at the moment', 'the timing of', 'at the point when'],
  'where': ['at which location', 'in which place', 'at what point', 'the position where', 'the site where'],
  'what': ['which thing', 'the item', 'the subject', 'the matter', 'the topic', 'that which'],
  'who': ['which person', 'the individual', 'the person responsible', 'the one who', 'the party that'],

  // Quantifiers
  'many': ['numerous', 'several', 'various', 'multiple', 'countless', 'abundant', 'plenty of', 'a multitude of'],
  'much': ['a great deal', 'considerable', 'substantial', 'significant', 'extensive', 'abundant', 'ample'],
  'most': ['the majority', 'nearly all', 'the greatest part', 'predominantly', 'primarily', 'largely'],
  'some': ['certain', 'particular', 'specific', 'various', 'a few', 'several', 'a number of', 'a portion of'],
  'all': ['every', 'each', 'the entire', 'the complete', 'the whole', 'total', 'everything', 'everyone'],
  'few': ['limited', 'scarce', 'minimal', 'handful', 'small number', 'not many', 'sparse', 'rare']
};

// Sentence starters for variety
const sentenceStarters = [
  'Furthermore,', 'Additionally,', 'Moreover,', 'In addition,', 'Similarly,',
  'Consequently,', 'As a result,', 'Therefore,', 'Hence,', 'Thus,',
  'On the other hand,', 'However,', 'Nevertheless,', 'Nonetheless,',
  'Interestingly,', 'Notably,', 'Remarkably,', 'Significantly,',
  'It should be noted that', 'It is worth mentioning that', 'One might argue that',
  'From this perspective,', 'In this context,', 'Given these circumstances,',
  'To illustrate,', 'For instance,', 'As an example,', 'Specifically,',
  'In particular,', 'Most importantly,', 'Above all,', 'Essentially,'
];

export function paraphraseText(text: string, options: ProcessingOptions): string {
  // Don't process if text is too short or looks like a header
  if (text.trim().length < 10 || /^#{1,6}\s/.test(text.trim())) {
    return text;
  }

  // Split text into sentences more carefully
  const sentences = text.split(/(?<=[.!?])\s+/).filter(s => s.trim().length > 0);

  // Determine replacement intensity (reduced for better quality)
  const replacementChance = getReplacementChance(options.intensity) * 0.7; // Reduce intensity

  const processedSentences = sentences.map((sentence, index) => {
    let result = sentence.trim();

    // Apply synonym replacement with better context awareness
    result = applySynonymReplacement(result, replacementChance);

    // Add sentence variety very sparingly and only when it makes sense
    if (index > 0 && sentences.length > 3 && Math.random() < 0.1) {
      const contextualStarters = ['Additionally', 'Furthermore', 'Moreover', 'However'];
      const starter = contextualStarters[Math.floor(Math.random() * contextualStarters.length)];

      // Only add if it doesn't already start with a transition
      if (!/^(Additionally|Furthermore|Moreover|However|Therefore|Consequently|Meanwhile|Similarly)/i.test(result)) {
        result = starter + ', ' + result.toLowerCase();
      }
    }

    // Apply style-specific transformations
    result = applyStyleTransformations(result, options.style);

    // Ensure proper capitalization
    result = result.charAt(0).toUpperCase() + result.slice(1);

    return result;
  });

  return processedSentences.join('. ').replace(/\.\s*\./g, '.') + (text.endsWith('.') ? '' : '.');
}

function getReplacementChance(intensity: string): number {
  switch (intensity) {
    case 'light': return 0.3;
    case 'medium': return 0.5;
    case 'heavy': return 0.7;
    default: return 0.5;
  }
}

function applySynonymReplacement(text: string, replacementChance: number): string {
  let result = text;
  
  Object.entries(synonyms).forEach(([word, replacements]) => {
    if (Math.random() < replacementChance) {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      const replacement = replacements[Math.floor(Math.random() * replacements.length)];
      result = result.replace(regex, replacement);
    }
  });
  
  return result;
}

function applyStyleTransformations(text: string, style: string): string {
  let result = text;
  
  switch (style) {
    case 'formal':
      result = expandContractions(result);
      result = addFormalTransitions(result);
      break;
    case 'casual':
      result = simplifyCasual(result);
      break;
    case 'academic':
      result = addAcademicPhrasing(result);
      break;
    case 'creative':
      result = addCreativeElements(result);
      break;
    case 'technical':
      result = addTechnicalPrecision(result);
      break;
    default:
      // Balanced style - no specific transformations
      break;
  }
  
  return result;
}

function expandContractions(text: string): string {
  const contractions: { [key: string]: string } = {
    "can't": "cannot",
    "won't": "will not",
    "don't": "do not",
    "isn't": "is not",
    "aren't": "are not",
    "wasn't": "was not",
    "weren't": "were not",
    "hasn't": "has not",
    "haven't": "have not",
    "hadn't": "had not",
    "wouldn't": "would not",
    "shouldn't": "should not",
    "couldn't": "could not",
    "doesn't": "does not",
    "didn't": "did not"
  };
  
  let result = text;
  Object.entries(contractions).forEach(([contraction, expansion]) => {
    const regex = new RegExp(contraction, 'gi');
    result = result.replace(regex, expansion);
  });
  
  return result;
}

function addFormalTransitions(text: string): string {
  // Only replace casual transitions with formal ones, don't add new ones
  return text
    .replace(/\bAlso\b/g, 'Additionally')
    .replace(/\bBut\b/g, 'However')
    .replace(/\bSo\b/g, 'Therefore')
    .replace(/\bStill\b/g, 'Nevertheless');
}

function simplifyCasual(text: string): string {
  return text
    .replace(/Furthermore/g, 'Also')
    .replace(/However/g, 'But')
    .replace(/Nevertheless/g, 'Still')
    .replace(/Therefore/g, 'So')
    .replace(/Subsequently/g, 'Then');
}

function addAcademicPhrasing(text: string): string {
  const academicPhrases = [
    'Research indicates that',
    'Studies have shown that',
    'Evidence suggests that',
    'It is noteworthy that'
  ];

  // Very rarely add academic framing, and only for longer sentences
  if (text.length > 50 && Math.random() < 0.1) {
    const phrase = academicPhrases[Math.floor(Math.random() * academicPhrases.length)];
    return phrase + ' ' + text.toLowerCase();
  }

  return text;
}

function addCreativeElements(text: string): string {
  // Add more expressive language and varied sentence structures
  return text
    .replace(/very/g, 'incredibly')
    .replace(/really/g, 'truly')
    .replace(/good/g, 'remarkable')
    .replace(/bad/g, 'troubling');
}

function addTechnicalPrecision(text: string): string {
  // Use more precise, technical language
  return text
    .replace(/use/g, 'implement')
    .replace(/make/g, 'generate')
    .replace(/show/g, 'demonstrate')
    .replace(/get/g, 'obtain');
}