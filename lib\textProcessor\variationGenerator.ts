import { ProcessingOptions } from '@/types';
import { paraphraseText } from './paraphraser';
import { optimizeStyle } from './styleOptimizer';

export function generateVariations(text: string, options: ProcessingOptions): string[] {
  const variations: string[] = [];
  
  // Generate base variation
  const baseVariation = processTextVariation(text, options);
  variations.push(baseVariation);
  
  if (options.addVariations) {
    // Generate light intensity variation
    const lightOptions = { ...options, intensity: 'light' as const };
    const lightVariation = processTextVariation(text, lightOptions);
    variations.push(lightVariation);

    // Generate heavy intensity variation
    const heavyOptions = { ...options, intensity: 'heavy' as const };
    const heavyVariation = processTextVariation(text, heavyOptions);
    variations.push(heavyVariation);
    
    // Generate alternative style variation
    const alternativeStyle = getAlternativeStyle(options.style);
    const styleOptions = { ...options, style: alternativeStyle };
    const styleVariation = processTextVariation(text, styleOptions);
    variations.push(styleVariation);
  }
  
  return variations;
}

function processTextVariation(text: string, options: ProcessingOptions): string {
  // Apply paraphrasing
  let result = paraphraseText(text, options);
  
  // Apply style optimization
  result = optimizeStyle(result, options);
  
  // Apply variation-specific enhancements
  result = applyVariationEnhancements(result, options);
  
  return result;
}

function applyVariationEnhancements(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // Add sentence structure variations
  result = varyStructure(result);
  
  // Add vocabulary variations
  result = varyVocabulary(result, options.intensity);
  
  // Add punctuation variations
  result = varyPunctuation(result);
  
  return result;
}

function varyStructure(text: string): string {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  const structureVariations = sentences.map((sentence, index) => {
    let result = sentence.trim();
    
    // Randomly apply different structural changes
    const changeType = Math.floor(Math.random() * 4);
    
    switch (changeType) {
      case 0:
        // Add introductory phrase
        result = addIntroductoryPhrase(result);
        break;
      case 1:
        // Convert to passive/active voice
        result = convertVoice(result);
        break;
      case 2:
        // Add subordinate clause
        result = addSubordinateClause(result);
        break;
      case 3:
        // Rearrange sentence elements
        result = rearrangeElements(result);
        break;
      default:
        // Keep original structure
        break;
    }
    
    return result;
  });
  
  return structureVariations.join('. ') + '.';
}

function addIntroductoryPhrase(sentence: string): string {
  const introductoryPhrases = [
    'Notably,',
    'Importantly,',
    'Significantly,',
    'Interestingly,',
    'Remarkably,',
    'Essentially,',
    'Fundamentally,',
    'Primarily,',
    'Basically,',
    'Generally,'
  ];
  
  if (Math.random() < 0.3) {
    const phrase = introductoryPhrases[Math.floor(Math.random() * introductoryPhrases.length)];
    return phrase + ' ' + sentence.toLowerCase();
  }
  
  return sentence;
}

function convertVoice(sentence: string): string {
  // Simple passive to active voice conversion patterns
  const passivePatterns = [
    { pattern: /(\w+) is (\w+ed) by (\w+)/, replacement: '$3 $2 $1' },
    { pattern: /(\w+) was (\w+ed) by (\w+)/, replacement: '$3 $2 $1' },
    { pattern: /(\w+) are (\w+ed) by (\w+)/, replacement: '$3 $2 $1' },
    { pattern: /(\w+) were (\w+ed) by (\w+)/, replacement: '$3 $2 $1' }
  ];
  
  let result = sentence;
  
  for (const { pattern, replacement } of passivePatterns) {
    if (pattern.test(result) && Math.random() < 0.4) {
      result = result.replace(pattern, replacement);
      break;
    }
  }
  
  return result;
}

function addSubordinateClause(sentence: string): string {
  const subordinateStarters = [
    'which',
    'that',
    'where',
    'when',
    'while',
    'although',
    'because',
    'since',
    'unless',
    'until'
  ];
  
  // Look for opportunities to add subordinate clauses
  if (Math.random() < 0.25) {
    const words = sentence.split(' ');
    const insertPoint = Math.floor(words.length / 2);
    const starter = subordinateStarters[Math.floor(Math.random() * subordinateStarters.length)];
    
    // Simple subordinate clause addition
    words.splice(insertPoint, 0, `,${starter}`);
    return words.join(' ');
  }
  
  return sentence;
}

function rearrangeElements(sentence: string): string {
  // Simple rearrangement patterns
  const words = sentence.split(' ');
  
  if (words.length > 6 && Math.random() < 0.3) {
    // Move adverbs or prepositional phrases
    const adverbPattern = /\b(quickly|slowly|carefully|easily|effectively|efficiently|successfully)\b/i;
    const match = sentence.match(adverbPattern);
    
    if (match) {
      const adverb = match[0];
      const withoutAdverb = sentence.replace(adverbPattern, '').trim();
      return `${adverb}, ${withoutAdverb}`;
    }
  }
  
  return sentence;
}

function varyVocabulary(text: string, intensity: string): string {
  // Additional vocabulary variations beyond basic synonyms
  const advancedSynonyms: { [key: string]: string[] } = {
    'analyze': ['examine', 'investigate', 'scrutinize', 'evaluate', 'assess', 'study'],
    'create': ['develop', 'generate', 'produce', 'construct', 'build', 'establish'],
    'improve': ['enhance', 'optimize', 'refine', 'upgrade', 'advance', 'perfect'],
    'understand': ['comprehend', 'grasp', 'perceive', 'recognize', 'realize', 'appreciate'],
    'implement': ['execute', 'apply', 'deploy', 'install', 'establish', 'introduce'],
    'demonstrate': ['show', 'illustrate', 'exhibit', 'display', 'present', 'reveal'],
    'significant': ['important', 'substantial', 'considerable', 'notable', 'remarkable', 'major'],
    'effective': ['efficient', 'successful', 'productive', 'powerful', 'impactful', 'potent'],
    'comprehensive': ['complete', 'thorough', 'extensive', 'detailed', 'exhaustive', 'full'],
    'innovative': ['creative', 'original', 'novel', 'groundbreaking', 'pioneering', 'inventive']
  };
  
  const replacementChance = intensity === 'light' ? 0.2 : intensity === 'medium' ? 0.4 : 0.6;
  
  let result = text;
  Object.entries(advancedSynonyms).forEach(([word, synonyms]) => {
    if (Math.random() < replacementChance) {
      const synonym = synonyms[Math.floor(Math.random() * synonyms.length)];
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      result = result.replace(regex, synonym);
    }
  });
  
  return result;
}

function varyPunctuation(text: string): string {
  let result = text;
  
  // Add variety in punctuation usage
  if (Math.random() < 0.3) {
    // Convert some periods to semicolons where appropriate
    result = result.replace(/\. ([A-Z][^.]*related|[A-Z][^.]*similar|[A-Z][^.]*addition)/g, '; $1');
  }
  
  if (Math.random() < 0.2) {
    // Add em dashes for emphasis
    result = result.replace(/ - /g, ' — ');
  }
  
  if (Math.random() < 0.25) {
    // Add parenthetical expressions
    const parentheticals = [
      '(notably)',
      '(importantly)',
      '(significantly)',
      '(interestingly)',
      '(remarkably)'
    ];
    
    const sentences = result.split('. ');
    const randomSentence = Math.floor(Math.random() * sentences.length);
    const parenthetical = parentheticals[Math.floor(Math.random() * parentheticals.length)];
    
    if (sentences[randomSentence]) {
      const words = sentences[randomSentence].split(' ');
      const insertPoint = Math.floor(words.length / 2);
      words.splice(insertPoint, 0, parenthetical);
      sentences[randomSentence] = words.join(' ');
      result = sentences.join('. ');
    }
  }
  
  return result;
}

function getAlternativeStyle(currentStyle: ProcessingOptions['style']): ProcessingOptions['style'] {
  const styleAlternatives: { [key in ProcessingOptions['style']]: ProcessingOptions['style'] } = {
    'formal': 'academic',
    'casual': 'balanced',
    'academic': 'technical',
    'creative': 'casual',
    'technical': 'formal',
    'balanced': 'creative'
  };

  return styleAlternatives[currentStyle] || 'balanced';
}

export function generateSingleVariation(text: string, options: ProcessingOptions, variationType: 'light' | 'heavy' | 'alternative'): string {
  let variationOptions = { ...options };
  
  switch (variationType) {
    case 'light':
      variationOptions.intensity = 'light';
      break;
    case 'heavy':
      variationOptions.intensity = 'heavy';
      break;
    case 'alternative':
      variationOptions.style = getAlternativeStyle(options.style);
      break;
  }
  
  return processTextVariation(text, variationOptions);
}