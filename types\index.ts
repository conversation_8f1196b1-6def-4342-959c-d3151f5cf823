export interface ProcessingOptions {
  intensity: 'light' | 'medium' | 'heavy';
  style: 'balanced' | 'formal' | 'casual' | 'academic' | 'creative' | 'technical';
  preserveFormat: boolean;
  addVariations: boolean;
}

export interface ProcessingResult {
  humanizedText: string;
  variations?: string[];
  improvementScore: number;
  detectionScore: number;
  originalAIDetectionScore: number;
  confidence: number;
  readabilityScore: number;
  processingTime: number;
  originalLength: number;
  newLength: number;
}

export interface DetectionBreakdown {
  sentenceStructure: string;
  vocabularyVariety: string;
  flowRhythm: string;
  styleConsistency: string;
}

export interface AIDetectionResult {
  score: number;
  confidence: number;
  breakdown: DetectionBreakdown;
  suggestions: string[];
}