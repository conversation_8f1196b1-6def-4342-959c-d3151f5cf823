'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Copy, Download, RotateCcw, CheckCircle } from 'lucide-react';
import { ProcessingResult } from '@/types';

interface OutputDisplayProps {
  originalText: string;
  result: ProcessingResult | null;
  isProcessing: boolean;
}

export default function OutputDisplay({ originalText, result, isProcessing }: OutputDisplayProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const handleCopy = (text: string, index: number) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  const handleDownload = (text: string, filename: string) => {
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (isProcessing) {
    return (
      <Card className="p-8 bg-white/5 backdrop-blur-lg border-white/10">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-white mb-2">Processing Your Text</h3>
          <p className="text-gray-400">Applying advanced humanization algorithms...</p>
        </div>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card className="p-8 bg-white/5 backdrop-blur-lg border-white/10">
        <div className="text-center text-gray-400">
          <RotateCcw className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-semibold mb-2">Ready to Process</h3>
          <p>Enter your AI-generated text above and click "Humanize Text" to begin.</p>
        </div>
      </Card>
    );
  }

  const variations = result.variations || [result.humanizedText];

  return (
    <Card className="p-6 bg-white/5 backdrop-blur-lg border-white/10">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">Humanized Output</h3>
        <div className="flex gap-2">
          <Badge variant="secondary" className="bg-green-500/20 text-green-400">
            {result.improvementScore}% More Human
          </Badge>
          <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
            {result.processingTime}ms
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="result" className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-slate-800/50">
          <TabsTrigger value="result" className="text-white">Humanized Text</TabsTrigger>
          <TabsTrigger value="comparison" className="text-white">Side by Side</TabsTrigger>
        </TabsList>

        <TabsContent value="result" className="mt-6">
          <div className="space-y-4">
            {variations.map((variation, index) => (
              <div key={index} className="bg-slate-800/30 rounded-lg p-4 border border-slate-700">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm text-gray-400">
                    {variations.length > 1 ? `Variation ${index + 1}` : 'Humanized Text'}
                  </span>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleCopy(variation, index)}
                      className="text-gray-400 hover:text-white"
                    >
                      {copiedIndex === index ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDownload(variation, `humanized-text-${index + 1}.txt`)}
                      className="text-gray-400 hover:text-white"
                    >
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <p className="text-white leading-relaxed whitespace-pre-wrap">{variation}</p>
              </div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="comparison" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-red-500/10 rounded-lg p-4 border border-red-500/20">
              <h4 className="text-sm font-semibold text-red-400 mb-3">Original (AI-Generated)</h4>
              <p className="text-gray-300 leading-relaxed whitespace-pre-wrap text-sm">{originalText}</p>
            </div>
            <div className="bg-green-500/10 rounded-lg p-4 border border-green-500/20">
              <h4 className="text-sm font-semibold text-green-400 mb-3">Humanized</h4>
              <p className="text-gray-300 leading-relaxed whitespace-pre-wrap text-sm">{result.humanizedText}</p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  );
}