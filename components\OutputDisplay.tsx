'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Copy, Download, RotateCcw, CheckCircle } from 'lucide-react';
import { ProcessingResult } from '@/types';

interface OutputDisplayProps {
  originalText: string;
  result: ProcessingResult | null;
  isProcessing: boolean;
}

export default function OutputDisplay({ originalText, result, isProcessing }: OutputDisplayProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const handleCopy = (text: string, index: number) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  const handleDownload = (text: string, filename: string) => {
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (isProcessing) {
    return (
      <Card className="p-8 bg-white/5 backdrop-blur-lg border-white/10">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-white mb-2">Processing Your Text</h3>
          <p className="text-gray-400">Applying advanced humanization algorithms...</p>
        </div>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card className="p-8 bg-white/5 backdrop-blur-lg border-white/10">
        <div className="text-center text-gray-400">
          <RotateCcw className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-semibold mb-2">Ready to Process</h3>
          <p>Enter your AI-generated text above and click "Humanize Text" to begin.</p>
        </div>
      </Card>
    );
  }

  const variations = result.variations || [result.humanizedText];

  return (
    <Card className="p-6 bg-white/5 backdrop-blur-lg border-white/10">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">Humanized Output</h3>
        <div className="flex gap-2">
          <Badge variant="secondary" className="bg-green-500/20 text-green-400">
            {result.improvementScore}% More Human
          </Badge>
          <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
            {result.processingTime}ms
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="result" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-slate-800/50">
          <TabsTrigger value="result" className="text-white">Humanized Text</TabsTrigger>
          <TabsTrigger value="analysis" className="text-white">AI Detection Analysis</TabsTrigger>
          <TabsTrigger value="comparison" className="text-white">Side by Side</TabsTrigger>
        </TabsList>

        <TabsContent value="result" className="mt-6">
          <div className="space-y-4">
            {variations.map((variation, index) => (
              <div key={index} className="bg-slate-800/30 rounded-lg p-4 border border-slate-700">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm text-gray-400">
                    {variations.length > 1 ? `Variation ${index + 1}` : 'Humanized Text'}
                  </span>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleCopy(variation, index)}
                      className="text-gray-400 hover:text-white"
                    >
                      {copiedIndex === index ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDownload(variation, `humanized-text-${index + 1}.txt`)}
                      className="text-gray-400 hover:text-white"
                    >
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <p className="text-white leading-relaxed whitespace-pre-wrap">{variation}</p>
              </div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analysis" className="mt-6">
          <div className="space-y-6">
            {/* AI Detection Comparison */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Before Analysis */}
              <div className="bg-red-500/10 rounded-lg p-4 border border-red-500/20">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-red-400">Before Humanization</h4>
                  <Badge variant="destructive" className="bg-red-600/20 text-red-400 border-red-600/30">
                    {result.originalAIDetectionScore}% AI Detection
                  </Badge>
                </div>
                <div className="space-y-3">
                  <div className="text-xs text-gray-400">
                    <div className="flex justify-between">
                      <span>Detection Risk:</span>
                      <span className="text-red-400 font-medium">
                        {result.originalAIDetectionScore > 70 ? 'HIGH' :
                         result.originalAIDetectionScore > 40 ? 'MEDIUM' : 'LOW'}
                      </span>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span>Text Length:</span>
                      <span>{result.originalLength} characters</span>
                    </div>
                  </div>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    {originalText.length > 150 ? originalText.substring(0, 150) + '...' : originalText}
                  </p>
                </div>
              </div>

              {/* After Analysis */}
              <div className="bg-green-500/10 rounded-lg p-4 border border-green-500/20">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-green-400">After Humanization</h4>
                  <Badge
                    variant={result.detectionScore < 30 ? "default" : result.detectionScore < 60 ? "secondary" : "destructive"}
                    className={
                      result.detectionScore < 30
                        ? "bg-green-600/20 text-green-400 border-green-600/30"
                        : result.detectionScore < 60
                        ? "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
                        : "bg-red-600/20 text-red-400 border-red-600/30"
                    }
                  >
                    {result.detectionScore}% AI Detection
                  </Badge>
                </div>
                <div className="space-y-3">
                  <div className="text-xs text-gray-400">
                    <div className="flex justify-between">
                      <span>Detection Risk:</span>
                      <span className={`font-medium ${
                        result.detectionScore < 30 ? 'text-green-400' :
                        result.detectionScore < 60 ? 'text-yellow-400' : 'text-red-400'
                      }`}>
                        {result.detectionScore > 70 ? 'HIGH' :
                         result.detectionScore > 40 ? 'MEDIUM' : 'LOW'}
                      </span>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span>Text Length:</span>
                      <span>{result.newLength} characters</span>
                    </div>
                  </div>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    {result.humanizedText.length > 150 ? result.humanizedText.substring(0, 150) + '...' : result.humanizedText}
                  </p>
                </div>
              </div>
            </div>

            {/* Improvement Metrics */}
            <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-700">
              <h4 className="text-sm font-semibold text-white mb-4">Humanization Metrics</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">
                    {Math.max(0, result.originalAIDetectionScore - result.detectionScore)}%
                  </div>
                  <div className="text-xs text-gray-400">AI Detection Reduced</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{result.improvementScore}%</div>
                  <div className="text-xs text-gray-400">Improvement Score</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{result.readabilityScore}</div>
                  <div className="text-xs text-gray-400">Readability Score</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">{result.confidence}%</div>
                  <div className="text-xs text-gray-400">Confidence Level</div>
                </div>
              </div>
            </div>

            {/* Processing Details */}
            <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-700">
              <h4 className="text-sm font-semibold text-white mb-3">Processing Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="flex justify-between py-1">
                    <span className="text-gray-400">Processing Time:</span>
                    <span className="text-white">{result.processingTime}ms</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span className="text-gray-400">Original Length:</span>
                    <span className="text-white">{result.originalLength} chars</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span className="text-gray-400">New Length:</span>
                    <span className="text-white">{result.newLength} chars</span>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between py-1">
                    <span className="text-gray-400">Length Change:</span>
                    <span className={`${result.newLength > result.originalLength ? 'text-blue-400' : 'text-green-400'}`}>
                      {result.newLength > result.originalLength ? '+' : ''}{result.newLength - result.originalLength} chars
                    </span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span className="text-gray-400">Variations Generated:</span>
                    <span className="text-white">{variations.length}</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span className="text-gray-400">Success Rate:</span>
                    <span className="text-green-400">
                      {result.originalAIDetectionScore > result.detectionScore ? 'Improved' : 'Maintained'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="comparison" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-red-500/10 rounded-lg p-4 border border-red-500/20">
              <h4 className="text-sm font-semibold text-red-400 mb-3">Original (AI-Generated)</h4>
              <p className="text-gray-300 leading-relaxed whitespace-pre-wrap text-sm">{originalText}</p>
            </div>
            <div className="bg-green-500/10 rounded-lg p-4 border border-green-500/20">
              <h4 className="text-sm font-semibold text-green-400 mb-3">Humanized</h4>
              <p className="text-gray-300 leading-relaxed whitespace-pre-wrap text-sm">{result.humanizedText}</p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  );
}