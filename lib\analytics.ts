// Plausible Analytics utility functions
declare global {
  interface Window {
    plausible?: (event: string, options?: { props?: Record<string, string | number> }) => void;
  }
}

export const analytics = {
  // Track text processing events
  trackTextProcessed: (options: {
    style: string;
    intensity: string;
    textLength: number;
    processingTime: number;
    hasVariations: boolean;
  }) => {
    if (typeof window !== 'undefined' && window.plausible) {
      window.plausible('Text Processed', {
        props: {
          style: options.style,
          intensity: options.intensity,
          textLength: options.textLength,
          processingTime: options.processingTime,
          hasVariations: options.hasVariations ? 'yes' : 'no'
        }
      });
    }
  },

  // Track copy actions
  trackCopy: (source: 'main' | 'variation', variationIndex?: number) => {
    if (typeof window !== 'undefined' && window.plausible) {
      window.plausible('Text Copied', {
        props: {
          source,
          variationIndex: variationIndex?.toString() || 'main'
        }
      });
    }
  },

  // Track download actions
  trackDownload: (source: 'main' | 'variation', variationIndex?: number) => {
    if (typeof window !== 'undefined' && window.plausible) {
      window.plausible('Text Downloaded', {
        props: {
          source,
          variationIndex: variationIndex?.toString() || 'main'
        }
      });
    }
  },

  // Track file uploads
  trackFileUpload: (fileType: string, fileSize: number) => {
    if (typeof window !== 'undefined' && window.plausible) {
      window.plausible('File Uploaded', {
        props: {
          fileType,
          fileSize: Math.round(fileSize / 1024) // Size in KB
        }
      });
    }
  },

  // Track style changes
  trackStyleChange: (fromStyle: string, toStyle: string) => {
    if (typeof window !== 'undefined' && window.plausible) {
      window.plausible('Style Changed', {
        props: {
          fromStyle,
          toStyle
        }
      });
    }
  },

  // Track intensity changes
  trackIntensityChange: (fromIntensity: string, toIntensity: string) => {
    if (typeof window !== 'undefined' && window.plausible) {
      window.plausible('Intensity Changed', {
        props: {
          fromIntensity,
          toIntensity
        }
      });
    }
  },

  // Track variations toggle
  trackVariationsToggle: (enabled: boolean) => {
    if (typeof window !== 'undefined' && window.plausible) {
      window.plausible('Variations Toggled', {
        props: {
          enabled: enabled ? 'yes' : 'no'
        }
      });
    }
  },

  // Track tab switches
  trackTabSwitch: (tab: string) => {
    if (typeof window !== 'undefined' && window.plausible) {
      window.plausible('Tab Switched', {
        props: {
          tab
        }
      });
    }
  },

  // Track errors
  trackError: (errorType: string, errorMessage: string) => {
    if (typeof window !== 'undefined' && window.plausible) {
      window.plausible('Error Occurred', {
        props: {
          errorType,
          errorMessage: errorMessage.substring(0, 100) // Limit message length
        }
      });
    }
  },

  // Track page views (automatic with Plausible script)
  trackPageView: () => {
    // Page views are automatically tracked by Plausible
    // This function is here for completeness
  }
};

export default analytics;
