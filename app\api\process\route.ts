import { NextRequest, NextResponse } from 'next/server';
import { processTextDirectly } from '@/lib/textProcessor';
import { ProcessingOptions } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text, options }: { text: string; options: ProcessingOptions } = body;

    if (!text || text.trim().length === 0) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    if (text.length > 50000) {
      return NextResponse.json(
        { error: 'Text is too long. Maximum 50,000 characters allowed.' },
        { status: 400 }
      );
    }

    // Process the text using the direct processor
    const result = processTextDirectly(text, options);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Processing error:', error);
    return NextResponse.json(
      { error: 'Failed to process text' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Text processing endpoint. Use POST method.' },
    { status: 200 }
  );
}