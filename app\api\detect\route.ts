import { NextRequest, NextResponse } from 'next/server';
import { analyzeAIDetection } from '@/lib/aiDetection/detector';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text }: { text: string } = body;

    if (!text || text.trim().length === 0) {
      return NextResponse.json(
        { error: 'Text is required for AI detection analysis' },
        { status: 400 }
      );
    }

    if (text.length > 10000) {
      return NextResponse.json(
        { error: 'Text is too long for detection analysis. Maximum 10,000 characters allowed.' },
        { status: 400 }
      );
    }

    // Analyze the text for AI detection patterns
    const result = analyzeAIDetection(text);

    return NextResponse.json(result);
  } catch (error) {
    console.error('AI detection error:', error);
    return NextResponse.json(
      { error: 'Failed to analyze text for AI detection' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'AI detection endpoint. Use POST method.' },
    { status: 200 }
  );
}